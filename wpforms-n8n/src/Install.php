<?php

namespace WPFormsAddonScaffold;

use WP_Site;

/**
 * Addon install.
 *
 * @since 1.0.0
 */
class Install {

	/**
	 * Primary class constructor.
	 *
	 * @since 1.0.0
	 */
	public function __construct() {

		$this->hooks();
	}

	/**
	 * Add hooks.
	 *
	 * @since 1.0.0
	 */
	private function hooks() {

		register_activation_hook( WPFORMS_ADDON_SCAFFOLD_FILE, [ $this, 'install' ] );

		add_action( 'wp_initialize_site', [ $this, 'new_multisite_blog' ], 10, 2 );
	}

	/**
	 * Perform certain actions on plugin activation.
	 *
	 * @since 1.0.0
	 *
	 * @param bool|mixed $network_wide Whether to enable the plugin for all sites in the network or just the current site.
	 *                                 Multisite only.
	 *                                 Default is false.
	 *
	 * @noinspection DisconnectedForeachInstructionInspection
	 */
	public function install( $network_wide = false ) {

		// Normal single site.
		if ( ! ( $network_wide && is_multisite() ) ) {
			$this->run();

			return;
		}

		// Multisite - go through each subsite and run the installer.
		$sites = get_sites(
			[
				'fields' => 'ids',
				'number' => 0,
			]
		);

		foreach ( $sites as $blog_id ) {
			switch_to_blog( $blog_id );
			$this->run();
			restore_current_blog();
		}
	}

	/**
	 * When a new site is created in multisite, see if we are network activated,
	 * and if so run the installer.
	 *
	 * @since 1.0.0
	 *
	 * @param WP_Site $new_site New site object.
	 * @param array   $args     Arguments for the initialization.
	 *
	 * @noinspection PhpUnusedParameterInspection
	 * @noinspection PhpMissingParamTypeInspection
	 */
	public function new_multisite_blog( $new_site, $args ) { // phpcs:ignore Generic.CodeAnalysis.UnusedFunctionParameter.FoundAfterLastUsed

		if ( is_plugin_active_for_network( plugin_basename( WPFORMS_ADDON_SCAFFOLD_FILE ) ) ) {
			switch_to_blog( $new_site->blog_id );
			$this->run();
			restore_current_blog();
		}
	}

	/**
	 * Run the actual installer.
	 *
	 * @since 1.0.0
	 */
	private function run() {}
}
